<script setup lang="ts">
import NoteDrawer from '@/components/NoteDrawer.vue';
import QuestionDetailDrawer from '@/components/QuestionDetailDrawer.vue';
import ShowAnswerDrawer from '@/components/ShowAnswerDrawer.vue';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { getKlgDetailApi, getProofBlockListApi, getQuesDetailApi } from '@/apis/path/klg';
import type { AreaListItem, ProofListItem, RefListItem } from '@/utils/type';
import { onMounted, ref, watch, shallowRef, toRaw, onUnmounted, triggerRef, nextTick } from 'vue';
import { KlgType, WorkerType } from '@/utils/constant';
import { getQuestionListApi, type QuestionData } from '@/apis/path/klg';
import { handleProof, convertContentToProofHTML } from '@/utils/lineWordFunction';
import { convertMathTagsToMDLatex } from '@/utils/latexUtils';
// 引入新的 composables
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import { useRenderManager } from '@/composables/useRenderManager';
import { useDrawerManager } from '@/composables/useDrawerManager';

const wordContent = ref();
const questionList = shallowRef<QuestionData[]>([]);
// 存储处理后的证明过程HTML
const proofContentHTML = ref<string>('');

// 使用抽屉管理 composable
const { initializeEventListeners, cleanupEventListeners } = useDrawerManager();

// 浮动弹窗相关状态
const floatingVisible = ref(false);
const floatingStyles = ref({});
const sameQuestionList = ref<any[]>([]);
const floatingElement = ref<HTMLElement>();

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon({
  onIconClick: (selectedText: string) => {
    handleAddQuestion(selectedText);
  }
});

// 使用Render管理器
const { initializeRender, destroyRenderInstance, reinitializeRender, addQuestion, removeQuestion } =
  useRenderManager({
    containerSelector: '#underline',
    getContentData: () => wordContent.value,
    questionList,
    onSelect: (data: any) => {
      // 当选中文本时，显示问号图标而不是直接打开弹窗
      if (data && data.content) {
        showQuestionIcon(data);
      } else {
        console.log('❌ 选中文本为空或无效');
      }
    },
    onClick: (data: any) => {
      // 修改为先显示浮动弹窗，再选择具体问题
      handleShowFloatingElement(data.target);
    },
    onFinish: (arg: any) => {
      const content = arg.content;
      console.log('myonFinish', content);
      wordContent.value = content;
    },
    enableDebugLog: true
  });

const props = defineProps({
  editable: Boolean,
  klgCode: String,
  dataForm: Object, // 从父组件传递的完整知识数据
  status: Number // 从父组件传递的状态
});
const dataForm = ref({
  klgCode: '',
  title: '',
  type: -1,
  author: '',
  saveTime: '',
  synList: [] as String[],
  areaList: [] as AreaListItem[],
  refList: [] as RefListItem[],
  proofList: [] as ProofListItem[],
  note: '',
  cnt: ''
});
const drawerRef = ref();
const curStatus = ref();

const handleQuestionList = async (klgCode: string) => {
  // 获取问题列表
  const res = await getQuestionListApi(klgCode);
  // 确保返回的数据是数组，如果不是则使用空数组
  questionList.value = Array.isArray(res.data) ? res.data : [];
  console.log('📋 问题列表获取成功:', questionList.value.length, '个问题');
};

// 处理添加问题
const handleAddQuestion = (words: string) => {
  const data = {
    mode: 1,
    associatedWords: convertMathTagsToMDLatex(words),
    keyword: convertMathTagsToMDLatex(words),
    klgCode: dataForm.value.klgCode,
    answers: [] // 添加空的answers数组，避免ComfirmDialog中的undefined错误
  };
  handleQuesDialog(data);
};

// 划词功能已通过 useRenderManager composable 管理
const handleQuesDialog = (data: any) => {
  emitter.emit(Event.SHOW_QUESTION_DIALOG, data);
};

// 浮动弹窗相关函数已移除，使用新的 QuestionDetailDrawer 组件
// 增加问题
const addQuestionFn = (data: any) => {
  // 使用RenderManager的addQuestion方法添加问题
  addQuestion(data.associatedWords, Number(data.questionId));

  // 如果是原理类型且证明过程RenderManager已初始化，也处理证明过程
  if (
    dataForm.value.type === KlgType.Principles &&
    proofRenderManager &&
    proofRenderManager.addQuestion
  ) {
    proofRenderManager.addQuestion(data.associatedWords, Number(data.questionId));
  }

  // 更新questionList
  questionList.value?.push({
    questionId: data.questionId,
    associatedWords: data.associatedWords
  });

  emitter.emit(Event.REFRESH_QUESTION, true);
};

// 删除问题
const removeQuestionFn = (questionId: string) => {
  // 找到要删除的问题
  const rawQuestionList = toRaw(questionList.value);
  const questionIndex = rawQuestionList?.findIndex((item) => item.questionId == questionId);

  if (questionIndex !== undefined && questionIndex !== -1 && rawQuestionList) {
    const questionToRemove = rawQuestionList[questionIndex];

    // 使用RenderManager的removeQuestion方法删除问题
    removeQuestion(questionToRemove.associatedWords, Number(questionId));

    // 如果是原理类型且证明过程RenderManager已初始化，也处理证明过程
    if (
      dataForm.value.type === KlgType.Principles &&
      proofRenderManager &&
      proofRenderManager.removeQuestion
    ) {
      proofRenderManager.removeQuestion(questionToRemove.associatedWords, Number(questionId));
    }

    // 从问题列表中移除
    rawQuestionList.splice(questionIndex, 1);
    triggerRef(questionList);
  }
};
// 抽屉点击事件监听已移除，使用新的 QuestionDetailDrawer 组件

// 显示浮动弹窗元素 - 优化版本，避免重复请求
const handleShowFloatingElement = async (questionData: HTMLElement) => {
  if (!questionData) {
    console.warn('⚠️ questionData 为空，跳过处理');
    return;
  }

  // 从 questionData 元素中获取 data-qid
  const qidAttr = questionData.getAttribute('data-qid');
  if (!qidAttr) {
    console.error('❌ 未找到问题ID属性');
    return;
  }

  const qids = qidAttr.split(',');

  try {
    // 获取问题详情
    const res = await getQuesDetailApi(qids);

    if (res.success && res.data.list && res.data.list.length > 0) {
      const questionList = res.data.list;
      // 无论有多少个问题，都先显示浮动选择弹窗
      sameQuestionList.value = questionList;

      // 计算浮动弹窗位置
      const rect = questionData.getBoundingClientRect();
      floatingStyles.value = {
        position: 'fixed',
        left: rect.left + 'px',
        top: rect.bottom + 5 + 'px',
        zIndex: 10001
      };

      // 显示浮动弹窗
      floatingVisible.value = true;
    } else {
      console.warn('⚠️ 未获取到有效的问题数据');
    }
  } catch (error) {
    console.error('❌ 获取问题详情失败:', error);
  }
};

// 处理浮动弹窗中问题的点击事件
const handleFloatingQuestionClick = (question: any) => {
  // 隐藏浮动弹窗
  floatingVisible.value = false;

  // 触发显示答案抽屉的自定义事件
  const showAnswerEvent = new CustomEvent('showAnswerFromFloating', {
    detail: { question }
  });
  window.dispatchEvent(showAnswerEvent);
  console.log('✅ 从浮动弹窗选择问题:', question);
};

// 处理点击外部区域隐藏浮动弹窗
const handleDocumentClickForFloating = (event: MouseEvent) => {
  if (floatingVisible.value && floatingElement.value) {
    const target = event.target as HTMLElement;
    // 如果点击的不是浮动弹窗内部，则隐藏弹窗
    if (!floatingElement.value.contains(target)) {
      floatingVisible.value = false;
    }
  }
};

// 移除重复的 handleShowAnswerDrawer 函数，统一使用 handleShowFloatingElement

// 证明过程的Render管理器（如果需要）
let proofRenderManager: any;

// 初始化证明过程的RenderManager
const initializeProofRenderManager = async (): Promise<void> => {
  if (dataForm.value.type !== KlgType.Principles) {
    // console.log(' 不是原理类型，跳过证明过程初始化');
    return;
  }
  const underLineProofHtml = handleProof(dataForm.value.proofList);
  console.log('underLineProofHtml', underLineProofHtml);

  proofRenderManager = useRenderManager({
    containerSelector: '.proof-container-content',
    getContentData: () => underLineProofHtml,
    questionList,
    onSelect: (data: any) => {
      if (data && data.content) {
        showQuestionIcon(data);
      } else {
        console.log('❌ 证明过程选中文本为空或无效');
      }
    },
    onClick: (data: any) => {
      // 证明过程中的问题点击也使用浮动弹窗逻辑
      handleShowFloatingElement(data.target);
    },
    onFinish: (arg: any) => {
      console.log('proofOnfinish', arg.content);
      // 将处理后的content转换为完整的HTML结构
      proofContentHTML.value = arg.content;
      console.log("proofContentHTML",proofContentHTML.value);
    },
    enableDebugLog: true
  });
};

// 监听props变化，处理异步数据传入的情况
watch(
  () => [props.dataForm, props.klgCode],
  async ([newKlgData, newKlgCode]) => {
  
    // 如果有新数据，则更新数据并初始化
    if (newKlgData && newKlgCode && (newKlgData as any)?.cnt) {

      // 使用深拷贝确保数据正确传递
      const oldProofList = dataForm.value.proofList;
      Object.assign(dataForm.value, props.dataForm);

      console.log('🔍 [KlgInfo.vue] Object.assign 后的 dataForm.value:', dataForm.value);
      console.log(
        '🔍 [KlgInfo.vue] Object.assign 后的 dataForm.value.proofList:',
        dataForm.value.proofList
      );

      // 只有在内容发生变化时才重新初始化
      const shouldReinitialize =
        !wordContent.value ||
        JSON.stringify(oldProofList) !== JSON.stringify(dataForm.value.proofList);

      if (shouldReinitialize) {
        console.log(
          '🔍 [KlgInfo.vue] 需要重新初始化，原因：',
          !wordContent.value ? '首次加载' : 'proofList发生变化'
        );
        wordContent.value = dataForm.value.cnt;
        await handleQuestionList(dataForm.value.klgCode);
        await initializeRender();
        await initializeProofRenderManager();
      }
    }
  },
  { deep: true, immediate: true }
);

// 专门监听 proofList 的变化
watch(
  () => (props.dataForm as any)?.proofList,
  async (newProofList, oldProofList) => {
    console.log('🔍 [KlgInfo.vue] proofList watch 触发');
    console.log('🔍 [KlgInfo.vue] newProofList:', newProofList);
    console.log('🔍 [KlgInfo.vue] oldProofList:', oldProofList);

    // 如果 proofList 发生变化且组件已经初始化，则重新初始化证明过程
    if (
      newProofList &&
      Array.isArray(newProofList) &&
      newProofList.length > 0 &&
      wordContent.value &&
      dataForm.value.type === KlgType.Principles
    ) {
      console.log('🔍 [KlgInfo.vue] proofList 发生变化，重新初始化证明过程');

      // 更新本地 proofList
      dataForm.value.proofList = newProofList;

      // 重新初始化证明过程
      await initializeProofRenderManager();
    }
  },
  { deep: true }
);

onMounted(async () => {
  // 注册事件监听
  emitter.emit(Event.SET_STATUS, props.status);
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);

  // 初始化抽屉管理器的事件监听
  initializeEventListeners();

  // 添加问号图标的全局点击事件监听
  document.addEventListener('click', handleDocumentClick as EventListener, true);

  // 添加浮动弹窗的全局点击事件监听
  document.addEventListener('click', handleDocumentClickForFloating as EventListener, true);
});
onUnmounted(() => {
  // 移除事件监听
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);

  // 清理抽屉管理器的事件监听
  cleanupEventListeners();

  // 移除问号图标的全局点击事件监听
  document.removeEventListener('click', handleDocumentClick as EventListener, true);

  // 移除浮动弹窗的全局点击事件监听
  document.removeEventListener('click', handleDocumentClickForFloating as EventListener, true);

  destroyRenderInstance();
  // proofRenderManager();
});

defineExpose({
  handleQuestionList
});
</script>
<template>
  <div class="wrapper">
    <div class="up-wrapper">
      <div class="main-container">
        <div class="content-container">
          <template v-if="wordContent">
            <div v-html="wordContent" id="underline"></div>
          </template>
        </div>
        <div class="proof-container" v-if="dataForm.type === KlgType.Principles">
          <div class="proof-container-title">证明过程</div>
          <div class="proof-container-content" v-html="proofContentHTML"></div>
        </div>
      </div>
    </div>
    <div class="down-wrapper">
      <div class="ref-container-title">参考文献</div>
      <span v-if="dataForm.refList.length === 0" class="ref-list" style="color: var(--color-grey)"
        >暂无文献</span
      >
      <div v-else class="ref-list">
        <span class="ref-line" v-for="ref in dataForm.refList" :key="ref.refId">
          <span class="ref-line-name">{{ ref.cntName }}</span>
          <span class="ref-line-chapter">{{ ref.indexPage ? ref.indexPage : '暂无信息' }}</span>
        </span>
      </div>
    </div>
  </div>
  <!-- other -->
  <NoteDrawer ref="drawerRef"></NoteDrawer>
  <QuestionDetailDrawer />
  <ShowAnswerDrawer />
  <div
    ref="floatingElement"
    :style="
      floatingVisible ? floatingStyles : { position: 'fixed', top: '-9999px', left: '-9999px' }
    "
    class="floatingContainer"
  >
    <Transition name="scale">
      <div
        v-if="floatingVisible && sameQuestionList && sameQuestionList.length > 0"
        class="floating-content"
      >
        <div
          v-for="question in sameQuestionList"
          :key="question.questionId"
          class="floating-content-item"
          @click="handleFloatingQuestionClick(question)"
        >
          <div style="display: flex; align-items: center">
            <span class="keyword-container">
              【

              <span
                class="questionList ellipsis-text-inline"
                style="word-break: break-all"
                v-html="question.keyword"
              ></span>

              】
            </span>
            <span v-if="question.questionType != '开放性问题'">{{ question.questionType }}</span>
            <span v-else v-html="question.questionDescription"></span>
          </div>
        </div>
      </div>
    </Transition>
  </div>
  <div
    v-if="questionIconVisible"
    ref="questionIconElement"
    class="question-icon"
    :style="{
      position: 'fixed',
      left: questionIconPosition.x + 'px',
      top: questionIconPosition.y + 'px',
      zIndex: 10000
    }"
    @click="handleQuestionIconClick"
  >
    <!-- 悬浮提示 -->
    <div class="question-tooltip">提问</div>
    <!-- 问号图标 -->
    <div class="question-icon-circle">
      <img src="@/assets/question.svg" alt="" />
    </div>
  </div>
</template>

<style scoped src="./css/KlgInfo.less"></style>
